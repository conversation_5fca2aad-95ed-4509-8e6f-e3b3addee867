"""
Compliance & Audit Framework with 2025 Standards
Automated compliance monitoring, audit trails, data retention, and regulatory reporting.
"""

import asyncio
import json
import uuid
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Union
import threading
import hashlib
import os
from pathlib import Path

import structlog

from ..core.logging_config import get_logger, CorrelationContext
from ..monitoring import increment_counter, set_gauge, record_timer


logger = get_logger(__name__)


class ComplianceStandard(Enum):
    """Supported compliance standards."""
    GDPR = "gdpr"
    CCPA = "ccpa"
    SOX = "sox"
    PCI_DSS = "pci_dss"
    HIPAA = "hipaa"
    ISO27001 = "iso27001"
    SOC2 = "soc2"


class DataCategory(Enum):
    """Categories of data for compliance classification."""
    PERSONAL_DATA = "personal_data"
    FINANCIAL_DATA = "financial_data"
    HEALTH_DATA = "health_data"
    BIOMETRIC_DATA = "biometric_data"
    BEHAVIORAL_DATA = "behavioral_data"
    TECHNICAL_DATA = "technical_data"
    PUBLIC_DATA = "public_data"


class AuditEventType(Enum):
    """Types of audit events."""
    DATA_ACCESS = "data_access"
    DATA_MODIFICATION = "data_modification"
    DATA_DELETION = "data_deletion"
    DATA_EXPORT = "data_export"
    USER_AUTHENTICATION = "user_authentication"
    PERMISSION_CHANGE = "permission_change"
    SYSTEM_CONFIGURATION = "system_configuration"
    COMPLIANCE_VIOLATION = "compliance_violation"
    SECURITY_INCIDENT = "security_incident"


class RiskLevel(Enum):
    """Risk levels for compliance violations."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class DataSubject:
    """Data subject information for GDPR compliance."""
    id: str
    email: Optional[str] = None
    consent_given: bool = False
    consent_timestamp: Optional[datetime] = None
    data_categories: Set[DataCategory] = field(default_factory=set)
    retention_period: Optional[timedelta] = None
    deletion_requested: bool = False
    deletion_request_date: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "email": self.email,
            "consent_given": self.consent_given,
            "consent_timestamp": self.consent_timestamp.isoformat() if self.consent_timestamp else None,
            "data_categories": [cat.value for cat in self.data_categories],
            "retention_period": self.retention_period.total_seconds() if self.retention_period else None,
            "deletion_requested": self.deletion_requested,
            "deletion_request_date": self.deletion_request_date.isoformat() if self.deletion_request_date else None
        }


@dataclass
class AuditEvent:
    """Audit event record."""
    id: str
    timestamp: datetime
    event_type: AuditEventType
    user_id: Optional[str]
    resource_id: Optional[str]
    action: str
    details: Dict[str, Any]
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    correlation_id: Optional[str] = None
    compliance_relevant: bool = True
    risk_level: RiskLevel = RiskLevel.LOW
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "event_type": self.event_type.value,
            "user_id": self.user_id,
            "resource_id": self.resource_id,
            "action": self.action,
            "details": self.details,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "correlation_id": self.correlation_id,
            "compliance_relevant": self.compliance_relevant,
            "risk_level": self.risk_level.value
        }
    
    def calculate_hash(self) -> str:
        """Calculate hash for audit event integrity."""
        event_str = json.dumps(self.to_dict(), sort_keys=True)
        return hashlib.sha256(event_str.encode()).hexdigest()


@dataclass
class ComplianceViolation:
    """Compliance violation record."""
    id: str
    timestamp: datetime
    standard: ComplianceStandard
    violation_type: str
    description: str
    risk_level: RiskLevel
    affected_data_subjects: List[str]
    remediation_required: bool
    remediation_deadline: Optional[datetime] = None
    resolved: bool = False
    resolution_timestamp: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "standard": self.standard.value,
            "violation_type": self.violation_type,
            "description": self.description,
            "risk_level": self.risk_level.value,
            "affected_data_subjects": self.affected_data_subjects,
            "remediation_required": self.remediation_required,
            "remediation_deadline": self.remediation_deadline.isoformat() if self.remediation_deadline else None,
            "resolved": self.resolved,
            "resolution_timestamp": self.resolution_timestamp.isoformat() if self.resolution_timestamp else None
        }


class AuditTrail:
    """Comprehensive audit trail system."""
    
    def __init__(self, storage_path: str = "audit_logs"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)
        
        self.events: deque = deque(maxlen=100000)  # In-memory buffer
        self.event_index: Dict[str, AuditEvent] = {}
        self.integrity_chain: List[str] = []  # Hash chain for integrity
        self._lock = threading.RLock()
        
        logger.info("AuditTrail initialized", storage_path=str(self.storage_path))
    
    def log_event(self, event_type: AuditEventType, user_id: Optional[str],
                  resource_id: Optional[str], action: str, details: Dict[str, Any],
                  ip_address: Optional[str] = None, user_agent: Optional[str] = None,
                  risk_level: RiskLevel = RiskLevel.LOW) -> str:
        """Log an audit event."""
        with self._lock:
            event_id = str(uuid.uuid4())
            
            event = AuditEvent(
                id=event_id,
                timestamp=datetime.now(timezone.utc),
                event_type=event_type,
                user_id=user_id,
                resource_id=resource_id,
                action=action,
                details=details,
                ip_address=ip_address,
                user_agent=user_agent,
                correlation_id=None,  # Would be populated from request context in real implementation
                risk_level=risk_level
            )
            
            # Add to in-memory storage
            self.events.append(event)
            self.event_index[event_id] = event
            
            # Update integrity chain
            event_hash = event.calculate_hash()
            if self.integrity_chain:
                # Chain with previous hash
                combined = self.integrity_chain[-1] + event_hash
                chain_hash = hashlib.sha256(combined.encode()).hexdigest()
            else:
                chain_hash = event_hash
            
            self.integrity_chain.append(chain_hash)
            
            # Persist to storage
            self._persist_event(event)
            
            # Update metrics
            increment_counter("audit_events", 1, {
                "event_type": event_type.value,
                "risk_level": risk_level.value
            })
            
            logger.info("Audit event logged",
                       event_id=event_id,
                       event_type=event_type.value,
                       user_id=user_id,
                       action=action,
                       risk_level=risk_level.value)
            
            return event_id
    
    def _persist_event(self, event: AuditEvent):
        """Persist audit event to storage."""
        try:
            # Create daily log file
            date_str = event.timestamp.strftime("%Y-%m-%d")
            log_file = self.storage_path / f"audit_{date_str}.jsonl"
            
            with open(log_file, "a") as f:
                f.write(json.dumps(event.to_dict()) + "\n")
                
        except Exception as e:
            logger.error("Failed to persist audit event", error=str(e), event_id=event.id)
    
    def get_events(self, start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None,
                   event_type: Optional[AuditEventType] = None,
                   user_id: Optional[str] = None,
                   limit: int = 1000) -> List[AuditEvent]:
        """Retrieve audit events with filtering."""
        with self._lock:
            filtered_events = []
            
            for event in reversed(self.events):  # Most recent first
                if len(filtered_events) >= limit:
                    break
                
                # Apply filters
                if start_time and event.timestamp < start_time:
                    continue
                if end_time and event.timestamp > end_time:
                    continue
                if event_type and event.event_type != event_type:
                    continue
                if user_id and event.user_id != user_id:
                    continue
                
                filtered_events.append(event)
            
            return filtered_events
    
    def verify_integrity(self) -> bool:
        """Verify audit trail integrity using hash chain."""
        with self._lock:
            if not self.events or not self.integrity_chain:
                return True
            
            # Recalculate hash chain
            recalculated_chain = []
            
            for i, event in enumerate(self.events):
                event_hash = event.calculate_hash()
                
                if recalculated_chain:
                    combined = recalculated_chain[-1] + event_hash
                    chain_hash = hashlib.sha256(combined.encode()).hexdigest()
                else:
                    chain_hash = event_hash
                
                recalculated_chain.append(chain_hash)
            
            # Compare with stored chain
            stored_chain = self.integrity_chain[-len(recalculated_chain):]
            integrity_valid = recalculated_chain == stored_chain
            
            if not integrity_valid:
                logger.error("Audit trail integrity violation detected")
                increment_counter("audit_integrity_violations", 1)
            
            return integrity_valid


class GDPRComplianceManager:
    """GDPR compliance management system."""
    
    def __init__(self, audit_trail: AuditTrail):
        self.audit_trail = audit_trail
        self.data_subjects: Dict[str, DataSubject] = {}
        self.consent_records: Dict[str, Dict[str, Any]] = {}
        self.data_processing_purposes: Dict[str, str] = {}
        self._lock = threading.RLock()
        
        # Default retention periods by data category
        self.default_retention_periods = {
            DataCategory.PERSONAL_DATA: timedelta(days=2555),  # 7 years
            DataCategory.FINANCIAL_DATA: timedelta(days=2555),  # 7 years
            DataCategory.BEHAVIORAL_DATA: timedelta(days=365),  # 1 year
            DataCategory.TECHNICAL_DATA: timedelta(days=90),   # 3 months
            DataCategory.PUBLIC_DATA: None  # No retention limit
        }
        
        logger.info("GDPRComplianceManager initialized")
    
    def register_data_subject(self, subject_id: str, email: Optional[str] = None,
                             data_categories: Optional[Set[DataCategory]] = None) -> DataSubject:
        """Register a new data subject."""
        with self._lock:
            if subject_id in self.data_subjects:
                return self.data_subjects[subject_id]
            
            data_subject = DataSubject(
                id=subject_id,
                email=email,
                data_categories=data_categories or set()
            )
            
            self.data_subjects[subject_id] = data_subject
            
            # Log audit event
            self.audit_trail.log_event(
                AuditEventType.DATA_ACCESS,
                None,
                subject_id,
                "register_data_subject",
                {"email": email, "data_categories": [cat.value for cat in (data_categories or set())]}
            )
            
            logger.info("Data subject registered", subject_id=subject_id, email=email)
            return data_subject
    
    def record_consent(self, subject_id: str, purpose: str, consent_given: bool,
                      legal_basis: str = "consent") -> bool:
        """Record consent for data processing."""
        with self._lock:
            if subject_id not in self.data_subjects:
                self.register_data_subject(subject_id)
            
            data_subject = self.data_subjects[subject_id]
            data_subject.consent_given = consent_given
            data_subject.consent_timestamp = datetime.now(timezone.utc)
            
            # Store detailed consent record
            consent_id = str(uuid.uuid4())
            self.consent_records[consent_id] = {
                "subject_id": subject_id,
                "purpose": purpose,
                "consent_given": consent_given,
                "legal_basis": legal_basis,
                "timestamp": data_subject.consent_timestamp.isoformat(),
                "ip_address": None,  # Would be populated from request context
                "user_agent": None   # Would be populated from request context
            }
            
            # Log audit event
            self.audit_trail.log_event(
                AuditEventType.DATA_MODIFICATION,
                None,
                subject_id,
                "record_consent",
                {
                    "purpose": purpose,
                    "consent_given": consent_given,
                    "legal_basis": legal_basis,
                    "consent_id": consent_id
                },
                risk_level=RiskLevel.MEDIUM
            )
            
            logger.info("Consent recorded",
                       subject_id=subject_id,
                       purpose=purpose,
                       consent_given=consent_given)
            
            return True
    
    def request_data_deletion(self, subject_id: str, reason: str = "user_request") -> bool:
        """Process data deletion request (Right to be Forgotten)."""
        with self._lock:
            if subject_id not in self.data_subjects:
                logger.warning("Data deletion requested for unknown subject", subject_id=subject_id)
                return False
            
            data_subject = self.data_subjects[subject_id]
            data_subject.deletion_requested = True
            data_subject.deletion_request_date = datetime.now(timezone.utc)
            
            # Log audit event
            self.audit_trail.log_event(
                AuditEventType.DATA_DELETION,
                None,
                subject_id,
                "request_data_deletion",
                {"reason": reason},
                risk_level=RiskLevel.HIGH
            )
            
            logger.warning("Data deletion requested",
                          subject_id=subject_id,
                          reason=reason)
            
            # Schedule actual deletion (would be implemented based on business logic)
            self._schedule_data_deletion(subject_id)
            
            return True
    
    def _schedule_data_deletion(self, subject_id: str):
        """Schedule data deletion (placeholder for actual implementation)."""
        # In a real implementation, this would:
        # 1. Identify all data associated with the subject
        # 2. Check for legal obligations to retain data
        # 3. Schedule deletion after any required retention period
        # 4. Notify relevant systems/databases
        
        logger.info("Data deletion scheduled", subject_id=subject_id)
    
    def check_retention_compliance(self) -> List[str]:
        """Check for data retention compliance violations."""
        violations = []
        current_time = datetime.now(timezone.utc)
        
        with self._lock:
            for subject_id, data_subject in self.data_subjects.items():
                if not data_subject.consent_timestamp:
                    continue
                
                for category in data_subject.data_categories:
                    retention_period = self.default_retention_periods.get(category)
                    if not retention_period:
                        continue
                    
                    data_age = current_time - data_subject.consent_timestamp
                    if data_age > retention_period:
                        violation_msg = f"Data retention violation for subject {subject_id}, category {category.value}"
                        violations.append(violation_msg)
                        
                        logger.warning("Data retention violation detected",
                                     subject_id=subject_id,
                                     category=category.value,
                                     data_age_days=data_age.days,
                                     retention_limit_days=retention_period.days)
        
        return violations
    
    def generate_compliance_report(self) -> Dict[str, Any]:
        """Generate GDPR compliance report."""
        with self._lock:
            current_time = datetime.now(timezone.utc)
            
            # Count data subjects by category
            category_counts = defaultdict(int)
            consent_stats = {"given": 0, "withdrawn": 0, "pending": 0}
            deletion_requests = 0
            
            for data_subject in self.data_subjects.values():
                for category in data_subject.data_categories:
                    category_counts[category.value] += 1
                
                if data_subject.consent_given:
                    consent_stats["given"] += 1
                elif data_subject.consent_timestamp:
                    consent_stats["withdrawn"] += 1
                else:
                    consent_stats["pending"] += 1
                
                if data_subject.deletion_requested:
                    deletion_requests += 1
            
            # Check retention compliance
            retention_violations = self.check_retention_compliance()
            
            report = {
                "report_timestamp": current_time.isoformat(),
                "data_subjects": {
                    "total_count": len(self.data_subjects),
                    "by_category": dict(category_counts)
                },
                "consent_management": consent_stats,
                "deletion_requests": deletion_requests,
                "retention_compliance": {
                    "violations_count": len(retention_violations),
                    "violations": retention_violations
                },
                "audit_events": {
                    "total_count": len(self.audit_trail.events),
                    "integrity_verified": self.audit_trail.verify_integrity()
                }
            }
            
            return report


class ComplianceViolationManager:
    """Manages compliance violations and remediation."""
    
    def __init__(self, audit_trail: AuditTrail):
        self.audit_trail = audit_trail
        self.violations: Dict[str, ComplianceViolation] = {}
        self._lock = threading.RLock()
        
        logger.info("ComplianceViolationManager initialized")
    
    def report_violation(self, standard: ComplianceStandard, violation_type: str,
                        description: str, risk_level: RiskLevel,
                        affected_data_subjects: List[str] = None) -> str:
        """Report a compliance violation."""
        with self._lock:
            violation_id = str(uuid.uuid4())
            
            violation = ComplianceViolation(
                id=violation_id,
                timestamp=datetime.now(timezone.utc),
                standard=standard,
                violation_type=violation_type,
                description=description,
                risk_level=risk_level,
                affected_data_subjects=affected_data_subjects or [],
                remediation_required=risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL],
                remediation_deadline=self._calculate_remediation_deadline(risk_level)
            )
            
            self.violations[violation_id] = violation
            
            # Log audit event
            self.audit_trail.log_event(
                AuditEventType.COMPLIANCE_VIOLATION,
                None,
                None,
                "report_violation",
                {
                    "violation_id": violation_id,
                    "standard": standard.value,
                    "violation_type": violation_type,
                    "risk_level": risk_level.value,
                    "affected_subjects_count": len(affected_data_subjects or [])
                },
                risk_level=risk_level
            )
            
            # Update metrics
            increment_counter("compliance_violations", 1, {
                "standard": standard.value,
                "risk_level": risk_level.value
            })
            
            logger.error("Compliance violation reported",
                        violation_id=violation_id,
                        standard=standard.value,
                        violation_type=violation_type,
                        risk_level=risk_level.value)
            
            return violation_id
    
    def _calculate_remediation_deadline(self, risk_level: RiskLevel) -> Optional[datetime]:
        """Calculate remediation deadline based on risk level."""
        if risk_level == RiskLevel.CRITICAL:
            return datetime.now(timezone.utc) + timedelta(hours=24)
        elif risk_level == RiskLevel.HIGH:
            return datetime.now(timezone.utc) + timedelta(days=7)
        elif risk_level == RiskLevel.MEDIUM:
            return datetime.now(timezone.utc) + timedelta(days=30)
        else:
            return None
    
    def resolve_violation(self, violation_id: str, resolution_notes: str) -> bool:
        """Mark a violation as resolved."""
        with self._lock:
            if violation_id not in self.violations:
                return False
            
            violation = self.violations[violation_id]
            violation.resolved = True
            violation.resolution_timestamp = datetime.now(timezone.utc)
            
            # Log audit event
            self.audit_trail.log_event(
                AuditEventType.COMPLIANCE_VIOLATION,
                None,
                None,
                "resolve_violation",
                {
                    "violation_id": violation_id,
                    "resolution_notes": resolution_notes
                },
                risk_level=RiskLevel.MEDIUM
            )
            
            logger.info("Compliance violation resolved",
                       violation_id=violation_id,
                       resolution_notes=resolution_notes)
            
            return True
    
    def get_open_violations(self, standard: Optional[ComplianceStandard] = None) -> List[ComplianceViolation]:
        """Get open violations, optionally filtered by standard."""
        with self._lock:
            violations = [v for v in self.violations.values() if not v.resolved]
            
            if standard:
                violations = [v for v in violations if v.standard == standard]
            
            return sorted(violations, key=lambda x: x.timestamp, reverse=True)


class ComplianceReportGenerator:
    """Generates comprehensive compliance reports."""

    def __init__(self, audit_trail: AuditTrail, gdpr_manager: GDPRComplianceManager,
                 violation_manager: ComplianceViolationManager):
        self.audit_trail = audit_trail
        self.gdpr_manager = gdpr_manager
        self.violation_manager = violation_manager

        logger.info("ComplianceReportGenerator initialized")

    def generate_comprehensive_report(self, start_date: Optional[datetime] = None,
                                    end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """Generate comprehensive compliance report."""
        if not start_date:
            start_date = datetime.now(timezone.utc) - timedelta(days=30)
        if not end_date:
            end_date = datetime.now(timezone.utc)

        # Get audit events for period
        audit_events = self.audit_trail.get_events(start_date, end_date, limit=10000)

        # Analyze events by type
        event_stats = defaultdict(int)
        risk_stats = defaultdict(int)
        user_activity = defaultdict(int)

        for event in audit_events:
            event_stats[event.event_type.value] += 1
            risk_stats[event.risk_level.value] += 1
            if event.user_id:
                user_activity[event.user_id] += 1

        # Get GDPR report
        gdpr_report = self.gdpr_manager.generate_compliance_report()

        # Get violations
        open_violations = self.violation_manager.get_open_violations()
        resolved_violations = [v for v in self.violation_manager.violations.values() if v.resolved]

        # Calculate compliance score
        compliance_score = self._calculate_compliance_score(open_violations, audit_events)

        report = {
            "report_metadata": {
                "generated_at": datetime.now(timezone.utc).isoformat(),
                "period_start": start_date.isoformat(),
                "period_end": end_date.isoformat(),
                "report_id": str(uuid.uuid4())
            },
            "audit_summary": {
                "total_events": len(audit_events),
                "events_by_type": dict(event_stats),
                "events_by_risk": dict(risk_stats),
                "unique_users": len(user_activity),
                "integrity_verified": self.audit_trail.verify_integrity()
            },
            "gdpr_compliance": gdpr_report,
            "violations": {
                "open_violations": len(open_violations),
                "resolved_violations": len(resolved_violations),
                "critical_violations": len([v for v in open_violations if v.risk_level == RiskLevel.CRITICAL]),
                "high_risk_violations": len([v for v in open_violations if v.risk_level == RiskLevel.HIGH])
            },
            "compliance_score": compliance_score,
            "recommendations": self._generate_recommendations(open_violations, audit_events)
        }

        return report

    def _calculate_compliance_score(self, violations: List[ComplianceViolation],
                                   audit_events: List[AuditEvent]) -> Dict[str, Any]:
        """Calculate overall compliance score."""
        base_score = 100.0

        # Deduct points for violations
        for violation in violations:
            if violation.risk_level == RiskLevel.CRITICAL:
                base_score -= 20
            elif violation.risk_level == RiskLevel.HIGH:
                base_score -= 10
            elif violation.risk_level == RiskLevel.MEDIUM:
                base_score -= 5
            else:
                base_score -= 1

        # Deduct points for high-risk audit events
        high_risk_events = [e for e in audit_events if e.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]]
        base_score -= len(high_risk_events) * 0.5

        # Ensure score doesn't go below 0
        final_score = max(0, base_score)

        # Determine grade
        if final_score >= 95:
            grade = "A+"
        elif final_score >= 90:
            grade = "A"
        elif final_score >= 85:
            grade = "B+"
        elif final_score >= 80:
            grade = "B"
        elif final_score >= 70:
            grade = "C"
        elif final_score >= 60:
            grade = "D"
        else:
            grade = "F"

        return {
            "score": final_score,
            "grade": grade,
            "total_violations": len(violations),
            "high_risk_events": len(high_risk_events)
        }

    def _generate_recommendations(self, violations: List[ComplianceViolation],
                                 audit_events: List[AuditEvent]) -> List[str]:
        """Generate compliance recommendations."""
        recommendations = []

        # Check for critical violations
        critical_violations = [v for v in violations if v.risk_level == RiskLevel.CRITICAL]
        if critical_violations:
            recommendations.append(f"URGENT: Address {len(critical_violations)} critical compliance violations immediately")

        # Check for high-risk events
        high_risk_events = [e for e in audit_events if e.risk_level == RiskLevel.CRITICAL]
        if len(high_risk_events) > 10:
            recommendations.append("Review security controls - high number of critical risk events detected")

        # Check audit trail integrity
        if not self.audit_trail.verify_integrity():
            recommendations.append("CRITICAL: Audit trail integrity compromised - investigate immediately")

        # Check GDPR compliance
        gdpr_report = self.gdpr_manager.generate_compliance_report()
        if gdpr_report["retention_compliance"]["violations_count"] > 0:
            recommendations.append("Address data retention violations to maintain GDPR compliance")

        # General recommendations
        if len(violations) > 5:
            recommendations.append("Implement additional compliance monitoring and controls")

        if not recommendations:
            recommendations.append("Compliance status is good - continue current practices")

        return recommendations


def get_compliance_status() -> Dict[str, Any]:
    """Get current compliance system status."""
    return {
        "audit_trail": {
            "total_events": len(audit_trail.events),
            "integrity_verified": audit_trail.verify_integrity(),
            "storage_path": str(audit_trail.storage_path)
        },
        "gdpr_compliance": {
            "registered_subjects": len(gdpr_manager.data_subjects),
            "consent_records": len(gdpr_manager.consent_records)
        },
        "violations": {
            "total_violations": len(violation_manager.violations),
            "open_violations": len(violation_manager.get_open_violations())
        },
        "timestamp": datetime.now(timezone.utc).isoformat()
    }


# Global instances
audit_trail = AuditTrail()
gdpr_manager = GDPRComplianceManager(audit_trail)
violation_manager = ComplianceViolationManager(audit_trail)
report_generator = ComplianceReportGenerator(audit_trail, gdpr_manager, violation_manager)
