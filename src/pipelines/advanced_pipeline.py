"""
Advanced Data Pipeline Architecture with 2025 Best Practices
Event-driven, fault-tolerant data processing with real-time streaming and batch capabilities.
"""

import asyncio
import json
import time
import uuid
from abc import ABC, abstractmethod
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Union, Callable, AsyncGenerator
import threading
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import pickle
import gzip
from pathlib import Path

import structlog

from ..core.logging_config import get_logger, CorrelationContext
from ..monitoring import increment_counter, set_gauge, record_timer, timer_context
from ..performance import profiled, cached


logger = get_logger(__name__)


class PipelineStage(Enum):
    """Pipeline processing stages."""
    INGESTION = "ingestion"
    VALIDATION = "validation"
    TRANSFORMATION = "transformation"
    ENRICHMENT = "enrichment"
    AGGREGATION = "aggregation"
    OUTPUT = "output"


class DataFormat(Enum):
    """Supported data formats."""
    JSON = "json"
    AVRO = "avro"
    PARQUET = "parquet"
    CSV = "csv"
    BINARY = "binary"


class ProcessingMode(Enum):
    """Data processing modes."""
    BATCH = "batch"
    STREAMING = "streaming"
    MICRO_BATCH = "micro_batch"


class PipelineStatus(Enum):
    """Pipeline execution status."""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    FAILED = "failed"
    COMPLETED = "completed"


@dataclass
class DataRecord:
    """Individual data record in the pipeline."""
    id: str
    data: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    source: Optional[str] = None
    schema_version: str = "1.0"
    processing_history: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert record to dictionary."""
        return {
            "id": self.id,
            "data": self.data,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat(),
            "source": self.source,
            "schema_version": self.schema_version,
            "processing_history": self.processing_history
        }
    
    def add_processing_step(self, step_name: str):
        """Add processing step to history."""
        self.processing_history.append(f"{step_name}@{datetime.now(timezone.utc).isoformat()}")


@dataclass
class PipelineMetrics:
    """Pipeline execution metrics."""
    records_processed: int = 0
    records_failed: int = 0
    processing_time: float = 0.0
    throughput: float = 0.0
    error_rate: float = 0.0
    last_updated: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    def update_metrics(self, processed: int, failed: int, duration: float):
        """Update pipeline metrics."""
        self.records_processed += processed
        self.records_failed += failed
        self.processing_time += duration
        
        total_records = self.records_processed + self.records_failed
        if total_records > 0:
            self.error_rate = self.records_failed / total_records
        
        if duration > 0:
            self.throughput = processed / duration
        
        self.last_updated = datetime.now(timezone.utc)


class PipelineProcessor(ABC):
    """Abstract base class for pipeline processors."""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        self.name = name
        self.config = config or {}
        self.metrics = PipelineMetrics()
        self._lock = threading.RLock()
        
        logger.info("PipelineProcessor initialized", name=name, config=config)
    
    @abstractmethod
    async def process(self, record: DataRecord) -> Optional[DataRecord]:
        """Process a single data record."""
        pass
    
    async def process_batch(self, records: List[DataRecord]) -> List[Optional[DataRecord]]:
        """Process a batch of records (default implementation)."""
        results = []
        for record in records:
            try:
                result = await self.process(record)
                results.append(result)
            except Exception as e:
                logger.error("Record processing failed", 
                           processor=self.name, 
                           record_id=record.id, 
                           error=str(e))
                results.append(None)
        return results
    
    def get_metrics(self) -> PipelineMetrics:
        """Get processor metrics."""
        with self._lock:
            return self.metrics


class ValidationProcessor(PipelineProcessor):
    """Data validation processor."""
    
    def __init__(self, name: str, schema: Dict[str, Any], config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.schema = schema
        self.required_fields = schema.get("required", [])
        self.field_types = schema.get("types", {})
        self.field_constraints = schema.get("constraints", {})
    
    @profiled("validation_processor")
    async def process(self, record: DataRecord) -> Optional[DataRecord]:
        """Validate data record against schema."""
        start_time = time.time()
        
        try:
            # Check required fields
            for field in self.required_fields:
                if field not in record.data:
                    raise ValueError(f"Missing required field: {field}")
            
            # Check field types
            for field, expected_type in self.field_types.items():
                if field in record.data:
                    value = record.data[field]
                    if not isinstance(value, expected_type):
                        raise TypeError(f"Field {field} has wrong type: expected {expected_type}, got {type(value)}")
            
            # Check field constraints
            for field, constraints in self.field_constraints.items():
                if field in record.data:
                    value = record.data[field]
                    
                    if "min" in constraints and value < constraints["min"]:
                        raise ValueError(f"Field {field} below minimum: {value} < {constraints['min']}")
                    
                    if "max" in constraints and value > constraints["max"]:
                        raise ValueError(f"Field {field} above maximum: {value} > {constraints['max']}")
                    
                    if "pattern" in constraints:
                        import re
                        if not re.match(constraints["pattern"], str(value)):
                            raise ValueError(f"Field {field} doesn't match pattern: {constraints['pattern']}")
            
            # Add validation step to processing history
            record.add_processing_step(f"validation:{self.name}")
            
            # Update metrics
            duration = time.time() - start_time
            with self._lock:
                self.metrics.update_metrics(1, 0, duration)
            
            return record
            
        except Exception as e:
            # Update metrics for failed validation
            duration = time.time() - start_time
            with self._lock:
                self.metrics.update_metrics(0, 1, duration)
            
            logger.warning("Validation failed", 
                         processor=self.name, 
                         record_id=record.id, 
                         error=str(e))
            
            # Add validation failure to metadata
            record.metadata["validation_errors"] = record.metadata.get("validation_errors", [])
            record.metadata["validation_errors"].append(str(e))
            
            return None  # Invalid record


class TransformationProcessor(PipelineProcessor):
    """Data transformation processor."""
    
    def __init__(self, name: str, transformations: List[Dict[str, Any]], config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.transformations = transformations
    
    @profiled("transformation_processor")
    async def process(self, record: DataRecord) -> Optional[DataRecord]:
        """Apply transformations to data record."""
        start_time = time.time()
        
        try:
            for transformation in self.transformations:
                transform_type = transformation.get("type")
                
                if transform_type == "field_mapping":
                    record = self._apply_field_mapping(record, transformation)
                elif transform_type == "field_calculation":
                    record = self._apply_field_calculation(record, transformation)
                elif transform_type == "field_formatting":
                    record = self._apply_field_formatting(record, transformation)
                elif transform_type == "field_filtering":
                    record = self._apply_field_filtering(record, transformation)
                else:
                    logger.warning("Unknown transformation type", type=transform_type)
            
            # Add transformation step to processing history
            record.add_processing_step(f"transformation:{self.name}")
            
            # Update metrics
            duration = time.time() - start_time
            with self._lock:
                self.metrics.update_metrics(1, 0, duration)
            
            return record
            
        except Exception as e:
            # Update metrics for failed transformation
            duration = time.time() - start_time
            with self._lock:
                self.metrics.update_metrics(0, 1, duration)
            
            logger.error("Transformation failed", 
                        processor=self.name, 
                        record_id=record.id, 
                        error=str(e))
            return None
    
    def _apply_field_mapping(self, record: DataRecord, transformation: Dict[str, Any]) -> DataRecord:
        """Apply field mapping transformation."""
        mapping = transformation.get("mapping", {})
        for old_field, new_field in mapping.items():
            if old_field in record.data:
                record.data[new_field] = record.data.pop(old_field)
        return record
    
    def _apply_field_calculation(self, record: DataRecord, transformation: Dict[str, Any]) -> DataRecord:
        """Apply field calculation transformation."""
        calculations = transformation.get("calculations", [])
        for calc in calculations:
            target_field = calc.get("target")
            expression = calc.get("expression")
            
            if target_field and expression:
                try:
                    # Simple expression evaluation (in production, use a safer evaluator)
                    result = eval(expression, {"__builtins__": {}}, record.data)
                    record.data[target_field] = result
                except Exception as e:
                    logger.warning("Calculation failed", 
                                 expression=expression, 
                                 error=str(e))
        return record
    
    def _apply_field_formatting(self, record: DataRecord, transformation: Dict[str, Any]) -> DataRecord:
        """Apply field formatting transformation."""
        formatting = transformation.get("formatting", {})
        for field, format_spec in formatting.items():
            if field in record.data:
                value = record.data[field]
                format_type = format_spec.get("type")
                
                if format_type == "datetime":
                    if isinstance(value, str):
                        from datetime import datetime
                        dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        record.data[field] = dt.strftime(format_spec.get("format", "%Y-%m-%d %H:%M:%S"))
                elif format_type == "number":
                    if isinstance(value, (int, float)):
                        precision = format_spec.get("precision", 2)
                        record.data[field] = round(value, precision)
                elif format_type == "string":
                    record.data[field] = str(value).strip()
        
        return record
    
    def _apply_field_filtering(self, record: DataRecord, transformation: Dict[str, Any]) -> DataRecord:
        """Apply field filtering transformation."""
        filter_config = transformation.get("filter", {})
        include_fields = filter_config.get("include")
        exclude_fields = filter_config.get("exclude")
        
        if include_fields:
            # Keep only specified fields
            filtered_data = {k: v for k, v in record.data.items() if k in include_fields}
            record.data = filtered_data
        elif exclude_fields:
            # Remove specified fields
            for field in exclude_fields:
                record.data.pop(field, None)
        
        return record


class EnrichmentProcessor(PipelineProcessor):
    """Data enrichment processor."""
    
    def __init__(self, name: str, enrichment_sources: List[Dict[str, Any]], config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.enrichment_sources = enrichment_sources
        self.cache = {}  # Simple cache for enrichment data
    
    @profiled("enrichment_processor")
    @cached(ttl_seconds=300)  # Cache enrichment results for 5 minutes
    async def process(self, record: DataRecord) -> Optional[DataRecord]:
        """Enrich data record with additional information."""
        start_time = time.time()
        
        try:
            for source in self.enrichment_sources:
                source_type = source.get("type")
                
                if source_type == "lookup_table":
                    record = await self._enrich_from_lookup_table(record, source)
                elif source_type == "api_call":
                    record = await self._enrich_from_api(record, source)
                elif source_type == "calculation":
                    record = await self._enrich_with_calculation(record, source)
                else:
                    logger.warning("Unknown enrichment source type", type=source_type)
            
            # Add enrichment step to processing history
            record.add_processing_step(f"enrichment:{self.name}")
            
            # Update metrics
            duration = time.time() - start_time
            with self._lock:
                self.metrics.update_metrics(1, 0, duration)
            
            return record
            
        except Exception as e:
            # Update metrics for failed enrichment
            duration = time.time() - start_time
            with self._lock:
                self.metrics.update_metrics(0, 1, duration)
            
            logger.error("Enrichment failed", 
                        processor=self.name, 
                        record_id=record.id, 
                        error=str(e))
            return record  # Return original record on enrichment failure
    
    async def _enrich_from_lookup_table(self, record: DataRecord, source: Dict[str, Any]) -> DataRecord:
        """Enrich from lookup table."""
        lookup_field = source.get("lookup_field")
        target_fields = source.get("target_fields", [])
        lookup_data = source.get("data", {})
        
        if lookup_field and lookup_field in record.data:
            lookup_key = record.data[lookup_field]
            if lookup_key in lookup_data:
                enrichment_data = lookup_data[lookup_key]
                for target_field in target_fields:
                    if target_field in enrichment_data:
                        record.data[f"enriched_{target_field}"] = enrichment_data[target_field]
        
        return record
    
    async def _enrich_from_api(self, record: DataRecord, source: Dict[str, Any]) -> DataRecord:
        """Enrich from API call (simulated)."""
        # In a real implementation, this would make actual API calls
        api_endpoint = source.get("endpoint")
        lookup_field = source.get("lookup_field")
        
        if lookup_field and lookup_field in record.data:
            # Simulate API enrichment
            await asyncio.sleep(0.01)  # Simulate API latency
            
            # Add simulated enrichment data
            record.data["api_enriched"] = True
            record.data["enrichment_timestamp"] = datetime.now(timezone.utc).isoformat()
        
        return record
    
    async def _enrich_with_calculation(self, record: DataRecord, source: Dict[str, Any]) -> DataRecord:
        """Enrich with calculated fields."""
        calculations = source.get("calculations", [])
        
        for calc in calculations:
            target_field = calc.get("target")
            formula = calc.get("formula")
            
            if target_field and formula:
                try:
                    # Simple calculation (in production, use a safer evaluator)
                    result = eval(formula, {"__builtins__": {}}, record.data)
                    record.data[target_field] = result
                except Exception as e:
                    logger.warning("Enrichment calculation failed", 
                                 formula=formula, 
                                 error=str(e))
        
        return record


class AggregationProcessor(PipelineProcessor):
    """Data aggregation processor."""
    
    def __init__(self, name: str, aggregation_config: Dict[str, Any], config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.aggregation_config = aggregation_config
        self.aggregation_buffer = defaultdict(list)
        self.window_size = aggregation_config.get("window_size", 100)
        self.group_by_fields = aggregation_config.get("group_by", [])
        self.aggregation_functions = aggregation_config.get("functions", {})
    
    @profiled("aggregation_processor")
    async def process(self, record: DataRecord) -> Optional[DataRecord]:
        """Process record for aggregation."""
        start_time = time.time()
        
        try:
            # Generate grouping key
            group_key = self._generate_group_key(record)
            
            # Add record to aggregation buffer
            self.aggregation_buffer[group_key].append(record)
            
            # Check if we should emit aggregated result
            if len(self.aggregation_buffer[group_key]) >= self.window_size:
                aggregated_record = self._create_aggregated_record(group_key)
                
                # Clear buffer for this group
                self.aggregation_buffer[group_key] = []
                
                # Update metrics
                duration = time.time() - start_time
                with self._lock:
                    self.metrics.update_metrics(1, 0, duration)
                
                return aggregated_record
            
            # No aggregated record to emit yet
            return None
            
        except Exception as e:
            # Update metrics for failed aggregation
            duration = time.time() - start_time
            with self._lock:
                self.metrics.update_metrics(0, 1, duration)
            
            logger.error("Aggregation failed", 
                        processor=self.name, 
                        record_id=record.id, 
                        error=str(e))
            return None
    
    def _generate_group_key(self, record: DataRecord) -> str:
        """Generate grouping key for record."""
        if not self.group_by_fields:
            return "default"
        
        key_parts = []
        for field in self.group_by_fields:
            value = record.data.get(field, "null")
            key_parts.append(f"{field}={value}")
        
        return "|".join(key_parts)
    
    def _create_aggregated_record(self, group_key: str) -> DataRecord:
        """Create aggregated record from buffer."""
        records = self.aggregation_buffer[group_key]
        
        if not records:
            return None
        
        # Initialize aggregated data
        aggregated_data = {}
        
        # Add group by fields
        if self.group_by_fields and records:
            for field in self.group_by_fields:
                aggregated_data[field] = records[0].data.get(field)
        
        # Apply aggregation functions
        for field, functions in self.aggregation_functions.items():
            values = [r.data.get(field) for r in records if field in r.data and r.data[field] is not None]
            
            if values:
                for func_name in functions:
                    if func_name == "count":
                        aggregated_data[f"{field}_count"] = len(values)
                    elif func_name == "sum":
                        aggregated_data[f"{field}_sum"] = sum(values)
                    elif func_name == "avg":
                        aggregated_data[f"{field}_avg"] = sum(values) / len(values)
                    elif func_name == "min":
                        aggregated_data[f"{field}_min"] = min(values)
                    elif func_name == "max":
                        aggregated_data[f"{field}_max"] = max(values)
        
        # Create aggregated record
        aggregated_record = DataRecord(
            id=str(uuid.uuid4()),
            data=aggregated_data,
            metadata={
                "aggregation_group": group_key,
                "record_count": len(records),
                "aggregation_window": self.window_size
            },
            source=f"aggregation:{self.name}"
        )
        
        aggregated_record.add_processing_step(f"aggregation:{self.name}")
        
        return aggregated_record


class DataPipeline:
    """Advanced data processing pipeline."""
    
    def __init__(self, name: str, processors: List[PipelineProcessor],
                 config: Optional[Dict[str, Any]] = None):
        self.name = name
        self.processors = processors
        self.config = config or {}
        self.status = PipelineStatus.IDLE
        self.metrics = PipelineMetrics()
        self.error_threshold = self.config.get("error_threshold", 0.1)  # 10% error rate threshold
        self.max_retries = self.config.get("max_retries", 3)
        self._lock = threading.RLock()

        logger.info("DataPipeline initialized",
                   name=name,
                   processors=[p.name for p in processors])
    
    @profiled("data_pipeline")
    async def process_record(self, record: DataRecord) -> Optional[DataRecord]:
        """Process a single record through the pipeline."""
        start_time = time.time()
        current_record = record
        
        try:
            with self._lock:
                if self.status != PipelineStatus.RUNNING:
                    self.status = PipelineStatus.RUNNING
            
            # Process through each stage
            for processor in self.processors:
                if current_record is None:
                    break
                
                # Process with retry logic
                retry_count = 0
                while retry_count <= self.max_retries:
                    try:
                        current_record = await processor.process(current_record)
                        break  # Success, exit retry loop
                    except Exception as e:
                        retry_count += 1
                        if retry_count > self.max_retries:
                            logger.error("Processor failed after retries", 
                                       processor=processor.name,
                                       record_id=record.id,
                                       retries=retry_count,
                                       error=str(e))
                            current_record = None
                            break
                        else:
                            logger.warning("Processor retry", 
                                         processor=processor.name,
                                         record_id=record.id,
                                         retry=retry_count,
                                         error=str(e))
                            await asyncio.sleep(0.1 * retry_count)  # Exponential backoff
            
            # Update pipeline metrics
            duration = time.time() - start_time
            success = current_record is not None
            
            with self._lock:
                if success:
                    self.metrics.update_metrics(1, 0, duration)
                else:
                    self.metrics.update_metrics(0, 1, duration)
                
                # Check error threshold
                if self.metrics.error_rate > self.error_threshold:
                    logger.error("Pipeline error rate exceeded threshold", 
                               pipeline=self.name,
                               error_rate=self.metrics.error_rate,
                               threshold=self.error_threshold)
                    self.status = PipelineStatus.FAILED
            
            return current_record
            
        except Exception as e:
            duration = time.time() - start_time
            with self._lock:
                self.metrics.update_metrics(0, 1, duration)
                self.status = PipelineStatus.FAILED
            
            logger.exception("Pipeline processing failed", 
                           pipeline=self.name, 
                           record_id=record.id, 
                           error=str(e))
            return None
    
    async def process_batch(self, records: List[DataRecord]) -> List[Optional[DataRecord]]:
        """Process a batch of records."""
        tasks = [self.process_record(record) for record in records]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions in results
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                logger.error("Batch processing exception", error=str(result))
                processed_results.append(None)
            else:
                processed_results.append(result)
        
        return processed_results
    
    def get_status(self) -> Dict[str, Any]:
        """Get pipeline status and metrics."""
        with self._lock:
            processor_metrics = {p.name: p.get_metrics() for p in self.processors}
            
            return {
                "name": self.name,
                "status": self.status.value,
                "metrics": {
                    "records_processed": self.metrics.records_processed,
                    "records_failed": self.metrics.records_failed,
                    "processing_time": self.metrics.processing_time,
                    "throughput": self.metrics.throughput,
                    "error_rate": self.metrics.error_rate,
                    "last_updated": self.metrics.last_updated.isoformat()
                },
                "processor_metrics": {
                    name: {
                        "records_processed": metrics.records_processed,
                        "records_failed": metrics.records_failed,
                        "throughput": metrics.throughput,
                        "error_rate": metrics.error_rate
                    }
                    for name, metrics in processor_metrics.items()
                },
                "config": self.config
            }
    
    def pause(self):
        """Pause pipeline execution."""
        with self._lock:
            if self.status == PipelineStatus.RUNNING:
                self.status = PipelineStatus.PAUSED
                logger.info("Pipeline paused", pipeline=self.name)
    
    def resume(self):
        """Resume pipeline execution."""
        with self._lock:
            if self.status == PipelineStatus.PAUSED:
                self.status = PipelineStatus.RUNNING
                logger.info("Pipeline resumed", pipeline=self.name)
    
    def reset(self):
        """Reset pipeline status and metrics."""
        with self._lock:
            self.status = PipelineStatus.IDLE
            self.metrics = PipelineMetrics()
            
            # Reset processor metrics
            for processor in self.processors:
                processor.metrics = PipelineMetrics()
            
            logger.info("Pipeline reset", pipeline=self.name)


class StreamingPipeline(DataPipeline):
    """Streaming data pipeline with real-time processing capabilities."""

    def __init__(self, name: str, processors: List[PipelineProcessor],
                 config: Optional[Dict[str, Any]] = None):
        super().__init__(name, processors, config)
        self.stream_buffer = asyncio.Queue(maxsize=config.get("buffer_size", 10000))
        self.processing_tasks = set()
        self.max_concurrent_tasks = config.get("max_concurrent_tasks", 100)
        self.batch_size = config.get("batch_size", 10)
        self.batch_timeout = config.get("batch_timeout", 1.0)  # seconds
        self._streaming = False

        logger.info("StreamingPipeline initialized",
                   name=name,
                   buffer_size=self.stream_buffer.maxsize,
                   max_concurrent_tasks=self.max_concurrent_tasks)

    async def start_streaming(self):
        """Start streaming processing."""
        if self._streaming:
            logger.warning("Streaming already started", pipeline=self.name)
            return

        self._streaming = True
        self.status = PipelineStatus.RUNNING

        # Start batch processing task
        batch_task = asyncio.create_task(self._batch_processor())
        self.processing_tasks.add(batch_task)
        batch_task.add_done_callback(self.processing_tasks.discard)

        logger.info("Streaming pipeline started", pipeline=self.name)

    async def stop_streaming(self):
        """Stop streaming processing."""
        if not self._streaming:
            return

        self._streaming = False

        # Cancel all processing tasks
        for task in self.processing_tasks:
            task.cancel()

        # Wait for tasks to complete
        if self.processing_tasks:
            await asyncio.gather(*self.processing_tasks, return_exceptions=True)

        self.status = PipelineStatus.IDLE
        logger.info("Streaming pipeline stopped", pipeline=self.name)

    async def add_record(self, record: DataRecord):
        """Add record to streaming pipeline."""
        if not self._streaming:
            raise RuntimeError("Pipeline not streaming")

        try:
            await self.stream_buffer.put(record)
            increment_counter("pipeline_records_queued", 1, {"pipeline": self.name})
        except asyncio.QueueFull:
            logger.error("Stream buffer full, dropping record",
                        pipeline=self.name,
                        record_id=record.id)
            increment_counter("pipeline_records_dropped", 1, {"pipeline": self.name})

    async def _batch_processor(self):
        """Process records in batches from the stream buffer."""
        while self._streaming:
            try:
                # Collect batch of records
                batch = []
                batch_start_time = time.time()

                # Wait for first record or timeout
                try:
                    first_record = await asyncio.wait_for(
                        self.stream_buffer.get(),
                        timeout=self.batch_timeout
                    )
                    batch.append(first_record)
                except asyncio.TimeoutError:
                    continue  # No records available, continue loop

                # Collect additional records up to batch size or timeout
                while len(batch) < self.batch_size and (time.time() - batch_start_time) < self.batch_timeout:
                    try:
                        record = await asyncio.wait_for(
                            self.stream_buffer.get(),
                            timeout=0.1
                        )
                        batch.append(record)
                    except asyncio.TimeoutError:
                        break  # No more records available quickly

                # Process batch if we have records
                if batch:
                    # Limit concurrent processing
                    while len(self.processing_tasks) >= self.max_concurrent_tasks:
                        await asyncio.sleep(0.01)

                    # Create processing task for batch
                    task = asyncio.create_task(self._process_batch_async(batch))
                    self.processing_tasks.add(task)
                    task.add_done_callback(self.processing_tasks.discard)

            except Exception as e:
                logger.exception("Batch processor error", pipeline=self.name, error=str(e))
                await asyncio.sleep(1)  # Back off on error

    async def _process_batch_async(self, batch: List[DataRecord]):
        """Process a batch of records asynchronously."""
        try:
            with timer_context(f"pipeline_batch_processing", {"pipeline": self.name}):
                results = await self.process_batch(batch)

                # Count successful and failed records
                successful = sum(1 for r in results if r is not None)
                failed = len(results) - successful

                increment_counter("pipeline_batch_processed", 1, {"pipeline": self.name})
                increment_counter("pipeline_records_successful", successful, {"pipeline": self.name})
                increment_counter("pipeline_records_failed", failed, {"pipeline": self.name})

                # Update throughput gauge
                set_gauge("pipeline_throughput", self.metrics.throughput, {"pipeline": self.name})

        except Exception as e:
            logger.exception("Batch processing failed", pipeline=self.name, error=str(e))

    def get_metrics(self) -> PipelineMetrics:
        """Get streaming pipeline metrics."""
        return self.metrics


class PipelineOrchestrator:
    """Orchestrates multiple pipelines with dependencies and scheduling."""

    def __init__(self):
        self.pipelines: Dict[str, DataPipeline] = {}
        self.dependencies: Dict[str, List[str]] = {}  # pipeline_name -> [dependency_names]
        self.schedules: Dict[str, Dict[str, Any]] = {}
        self.execution_history: deque = deque(maxlen=1000)
        self._lock = threading.RLock()

        logger.info("PipelineOrchestrator initialized")

    def register_pipeline(self, pipeline: DataPipeline, dependencies: Optional[List[str]] = None):
        """Register a pipeline with optional dependencies."""
        with self._lock:
            self.pipelines[pipeline.name] = pipeline
            self.dependencies[pipeline.name] = dependencies or []

            logger.info("Pipeline registered",
                       pipeline=pipeline.name,
                       dependencies=dependencies or [])

    def set_schedule(self, pipeline_name: str, schedule_config: Dict[str, Any]):
        """Set schedule for a pipeline."""
        with self._lock:
            if pipeline_name not in self.pipelines:
                raise ValueError(f"Pipeline {pipeline_name} not registered")

            self.schedules[pipeline_name] = schedule_config
            logger.info("Pipeline schedule set",
                       pipeline=pipeline_name,
                       schedule=schedule_config)

    async def execute_pipeline(self, pipeline_name: str, input_data: List[DataRecord]) -> List[Optional[DataRecord]]:
        """Execute a specific pipeline."""
        with self._lock:
            if pipeline_name not in self.pipelines:
                raise ValueError(f"Pipeline {pipeline_name} not registered")

            pipeline = self.pipelines[pipeline_name]

        # Check dependencies
        await self._check_dependencies(pipeline_name)

        # Execute pipeline
        start_time = time.time()

        try:
            logger.info("Executing pipeline", pipeline=pipeline_name, input_records=len(input_data))

            results = await pipeline.process_batch(input_data)

            duration = time.time() - start_time
            successful_results = [r for r in results if r is not None]

            # Record execution history
            execution_record = {
                "pipeline": pipeline_name,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "input_count": len(input_data),
                "output_count": len(successful_results),
                "duration": duration,
                "success_rate": len(successful_results) / len(input_data) if input_data else 0
            }

            with self._lock:
                self.execution_history.append(execution_record)

            logger.info("Pipeline execution completed",
                       pipeline=pipeline_name,
                       duration=duration,
                       success_rate=execution_record["success_rate"])

            return results

        except Exception as e:
            duration = time.time() - start_time

            # Record failed execution
            execution_record = {
                "pipeline": pipeline_name,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "input_count": len(input_data),
                "output_count": 0,
                "duration": duration,
                "success_rate": 0,
                "error": str(e)
            }

            with self._lock:
                self.execution_history.append(execution_record)

            logger.exception("Pipeline execution failed",
                           pipeline=pipeline_name,
                           error=str(e))
            raise

    async def _check_dependencies(self, pipeline_name: str):
        """Check if pipeline dependencies are satisfied."""
        dependencies = self.dependencies.get(pipeline_name, [])

        for dep_name in dependencies:
            if dep_name not in self.pipelines:
                raise ValueError(f"Dependency pipeline {dep_name} not registered")

            dep_pipeline = self.pipelines[dep_name]

            # Check if dependency pipeline is in a valid state
            if dep_pipeline.status == PipelineStatus.FAILED:
                raise RuntimeError(f"Dependency pipeline {dep_name} is in failed state")

            # For simplicity, we assume dependencies are satisfied if they're not failed
            # In a real implementation, you might check completion timestamps, etc.

    async def execute_all_pipelines(self, input_data_map: Dict[str, List[DataRecord]]) -> Dict[str, List[Optional[DataRecord]]]:
        """Execute all pipelines in dependency order."""
        # Topological sort of pipelines based on dependencies
        execution_order = self._topological_sort()

        results = {}

        for pipeline_name in execution_order:
            input_data = input_data_map.get(pipeline_name, [])

            if input_data or not self.dependencies.get(pipeline_name):
                # Execute pipeline if it has input data or no dependencies
                try:
                    pipeline_results = await self.execute_pipeline(pipeline_name, input_data)
                    results[pipeline_name] = pipeline_results
                except Exception as e:
                    logger.error("Pipeline execution failed in orchestration",
                               pipeline=pipeline_name,
                               error=str(e))
                    results[pipeline_name] = []

        return results

    def _topological_sort(self) -> List[str]:
        """Perform topological sort of pipelines based on dependencies."""
        # Simple topological sort implementation
        visited = set()
        temp_visited = set()
        result = []

        def visit(pipeline_name: str):
            if pipeline_name in temp_visited:
                raise ValueError(f"Circular dependency detected involving {pipeline_name}")

            if pipeline_name not in visited:
                temp_visited.add(pipeline_name)

                # Visit dependencies first
                for dep in self.dependencies.get(pipeline_name, []):
                    visit(dep)

                temp_visited.remove(pipeline_name)
                visited.add(pipeline_name)
                result.append(pipeline_name)

        # Visit all pipelines
        for pipeline_name in self.pipelines.keys():
            if pipeline_name not in visited:
                visit(pipeline_name)

        return result

    def get_orchestrator_status(self) -> Dict[str, Any]:
        """Get orchestrator status and metrics."""
        with self._lock:
            pipeline_statuses = {
                name: pipeline.get_status()
                for name, pipeline in self.pipelines.items()
            }

            recent_executions = list(self.execution_history)[-10:]  # Last 10 executions

            return {
                "registered_pipelines": len(self.pipelines),
                "pipeline_statuses": pipeline_statuses,
                "dependencies": dict(self.dependencies),
                "schedules": dict(self.schedules),
                "recent_executions": recent_executions,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }


def get_pipeline_status() -> Dict[str, Any]:
    """Get comprehensive pipeline system status."""
    return {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "system_info": {
            "available_processors": [
                "ValidationProcessor",
                "TransformationProcessor",
                "EnrichmentProcessor",
                "AggregationProcessor"
            ],
            "supported_formats": [fmt.value for fmt in DataFormat],
            "processing_modes": [mode.value for mode in ProcessingMode]
        }
    }


# Global orchestrator instance
pipeline_orchestrator = PipelineOrchestrator()
