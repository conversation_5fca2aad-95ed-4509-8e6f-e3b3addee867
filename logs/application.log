{"event": "\u001b[2m2025-07-10T04:58:43.328276Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mLogging system initialized    \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtoken_analyzer.logging\u001b[0m]\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35ma8600ee5-643a-420c-93f0-d3052d67496f\u001b[0m \u001b[36mcorrelation_tracking\u001b[0m=\u001b[35mTrue\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mlog_level\u001b[0m=\u001b[35mINFO\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mstructured_logging\u001b[0m=\u001b[35mTrue\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523327\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.328638Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mInfo message with metrics     \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.basic\u001b[0m]\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35ma8600ee5-643a-420c-93f0-d3052d67496f\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mresponse_time\u001b[0m=\u001b[35m0.123\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35msuccess\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523328\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.328804Z\u001b[0m [\u001b[33m\u001b[1mwarning  \u001b[0m] \u001b[1mWarning message               \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.basic\u001b[0m]\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35ma8600ee5-643a-420c-93f0-d3052d67496f\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mrisk_level\u001b[0m=\u001b[35mmedium\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523328\u001b[0m \u001b[36mtoken\u001b[0m=\u001b[35m[REDACTED]\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.328926Z\u001b[0m [\u001b[31m\u001b[1merror    \u001b[0m] \u001b[1mError message                 \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.basic\u001b[0m]\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35ma8600ee5-643a-420c-93f0-d3052d67496f\u001b[0m \u001b[36mdetails\u001b[0m=\u001b[35m'Test error'\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36merror_code\u001b[0m=\u001b[35mE001\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523328\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.329054Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mMessage with manual correlation ID\u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.correlation\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35mmanual_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mtest-correlation-123\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523329\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.329159Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mMessage within correlation context\u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.correlation\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35mcontext_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mcontext-test-456\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523329\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.329264Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mOuter context message         \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.correlation\u001b[0m]\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mouter-context\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523329\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.329357Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mInner context message         \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.correlation\u001b[0m]\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35minner-context\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523329\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.329475Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mProcessing user request       \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.request\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35mlogin\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mtest-correlation-123\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mip\u001b[0m=\u001b[35m127.0.0.1\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mrequest_id\u001b[0m=\u001b[35mreq-123\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523329\u001b[0m \u001b[36muser_id\u001b[0m=\u001b[35muser-456\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.329574Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRequest completed             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.request\u001b[0m]\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mtest-correlation-123\u001b[0m \u001b[36mduration\u001b[0m=\u001b[35m0.234\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mrequest_id\u001b[0m=\u001b[35mreq-123\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35msuccess\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523329\u001b[0m \u001b[36muser_id\u001b[0m=\u001b[35muser-456\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.329669Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAnonymous request             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.request\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35mview\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mtest-correlation-123\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mrequest_id\u001b[0m=\u001b[35mreq-789\u001b[0m \u001b[36mresource\u001b[0m=\u001b[35mtoken_list\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523329\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.341005Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAsync log message             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.async\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35masync_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mworker-0\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mmessage_id\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523340\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m \u001b[36mworker_id\u001b[0m=\u001b[35m0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.341270Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAsync log message             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.async\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35masync_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mworker-1\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mmessage_id\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523341\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m \u001b[36mworker_id\u001b[0m=\u001b[35m1\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.341448Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAsync log message             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.async\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35masync_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mworker-2\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mmessage_id\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523341\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m \u001b[36mworker_id\u001b[0m=\u001b[35m2\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.352427Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAsync log message             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.async\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35masync_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mworker-0\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mmessage_id\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523352\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m \u001b[36mworker_id\u001b[0m=\u001b[35m0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.352745Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAsync log message             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.async\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35masync_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mworker-1\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mmessage_id\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523352\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m \u001b[36mworker_id\u001b[0m=\u001b[35m1\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.352953Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAsync log message             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.async\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35masync_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mworker-2\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mmessage_id\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523352\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m \u001b[36mworker_id\u001b[0m=\u001b[35m2\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.363725Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAsync log message             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.async\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35masync_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mworker-0\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mmessage_id\u001b[0m=\u001b[35m2\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523363\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m \u001b[36mworker_id\u001b[0m=\u001b[35m0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.364036Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAsync log message             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.async\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35masync_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mworker-1\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mmessage_id\u001b[0m=\u001b[35m2\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523364\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m \u001b[36mworker_id\u001b[0m=\u001b[35m1\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.364182Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAsync log message             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.async\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35masync_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mworker-2\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mmessage_id\u001b[0m=\u001b[35m2\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523364\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m \u001b[36mworker_id\u001b[0m=\u001b[35m2\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.374806Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAsync log message             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.async\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35masync_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mworker-0\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mmessage_id\u001b[0m=\u001b[35m3\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523374\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m \u001b[36mworker_id\u001b[0m=\u001b[35m0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.375054Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAsync log message             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.async\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35masync_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mworker-1\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mmessage_id\u001b[0m=\u001b[35m3\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523375\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m \u001b[36mworker_id\u001b[0m=\u001b[35m1\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.375226Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAsync log message             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.async\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35masync_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mworker-2\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mmessage_id\u001b[0m=\u001b[35m3\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523375\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m \u001b[36mworker_id\u001b[0m=\u001b[35m2\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.386220Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAsync log message             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.async\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35masync_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mworker-0\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mmessage_id\u001b[0m=\u001b[35m4\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523386\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m \u001b[36mworker_id\u001b[0m=\u001b[35m0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.386493Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAsync log message             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.async\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35masync_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mworker-1\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mmessage_id\u001b[0m=\u001b[35m4\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523386\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m \u001b[36mworker_id\u001b[0m=\u001b[35m1\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.386604Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAsync log message             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.async\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35masync_test\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mworker-2\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mmessage_id\u001b[0m=\u001b[35m4\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523386\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m \u001b[36mworker_id\u001b[0m=\u001b[35m2\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.386877Z\u001b[0m [\u001b[31m\u001b[1merror    \u001b[0m] \u001b[1mCaught test exception         \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.errors\u001b[0m]\u001b[0m \u001b[36mcomponent\u001b[0m=\u001b[35mtest_suite\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mtest-correlation-123\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36merror_message\u001b[0m=\u001b[35m'Test error for logging'\u001b[0m \u001b[36merror_type\u001b[0m=\u001b[35mValueError\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523386\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m\nTraceback (most recent call last):\n  File \"/Users/<USER>/ar/test_enhanced_logging.py\", line 121, in test_error_handling\n    raise ValueError(\"Test error for logging\")\nValueError: Test error for logging", "exc_info": ["<class 'ValueError'>", "ValueError('Test error for logging')", "<traceback object at 0x1078bd7c0>"]}
{"event": "\u001b[2m2025-07-10T04:58:43.423880Z\u001b[0m [\u001b[31m\u001b[1merror    \u001b[0m] \u001b[1mStructured error log          \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.errors\u001b[0m]\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mtest-correlation-123\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36merror_category\u001b[0m=\u001b[35mvalidation\u001b[0m \u001b[36merror_code\u001b[0m=\u001b[35mTEST_001\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mrecovery_suggestion\u001b[0m=\u001b[35m'Check input parameters'\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523423\u001b[0m \u001b[36muser_action\u001b[0m=\u001b[35mtoken_analysis\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.424050Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mUser authentication           \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.security\u001b[0m]\u001b[0m \u001b[36maction\u001b[0m=\u001b[35mlogin\u001b[0m \u001b[36mapi_key\u001b[0m=\u001b[35m[REDACTED]\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mtest-correlation-123\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mpassword\u001b[0m=\u001b[35m[REDACTED]\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523424\u001b[0m \u001b[36mtoken\u001b[0m=\u001b[35m[REDACTED]\u001b[0m \u001b[36muser_id\u001b[0m=\u001b[35muser-123\u001b[0m \u001b[36musername\u001b[0m=\u001b[35mtestuser\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m"}
{"event": "\u001b[2m2025-07-10T04:58:43.424170Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAPI request                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mtest.security\u001b[0m]\u001b[0m \u001b[36mcorrelation_id\u001b[0m=\u001b[35mtest-correlation-123\u001b[0m \u001b[36menvironment\u001b[0m=\u001b[35mdevelopment\u001b[0m \u001b[36mhostname\u001b[0m=\u001b[35mSs-MacBook-Pro.local\u001b[0m \u001b[36mprocess_id\u001b[0m=\u001b[35m55966\u001b[0m \u001b[36mrequest_data\u001b[0m=\u001b[35m{'user': 'testuser', 'credentials': '[REDACTED]', 'metadata': {'ip': '127.0.0.1', 'user_agent': 'test-agent'}}\u001b[0m \u001b[36mservice\u001b[0m=\u001b[35mtoken-analyzer\u001b[0m \u001b[36mtimestamp_ms\u001b[0m=\u001b[35m1752141523424\u001b[0m \u001b[36mversion\u001b[0m=\u001b[35m1.0.0\u001b[0m"}
